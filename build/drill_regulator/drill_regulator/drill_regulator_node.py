from dataclasses import dataclass
from typing import Optional, Dict, Any

import rclpy
from rclpy.qos import QoSProfile, DurabilityPolicy, ReliabilityPolicy

from base_node import BaseNode, PID
from drill_msgs.msg import (
    DrillCtrl,
    DrillActuatorCtrl,
    DrillState,
    StateMachineStatus,
    BoolStamped,
    Permission,
)


@dataclass(slots=True)
class RegulatorState:
    """Internal regulator state."""
    # Setpoints
    feed_setpoint: float = 0.0
    rotation_setpoint: float = 0.0
    pressure_setpoint: float = 0.0
    torque_limit: float = 0.0

    # Feedback
    feed_feedback: float = 0.0
    rotation_feedback: float = 0.0
    pressure_feedback: float = 0.0

    # PID outputs (-1..1)
    feed_output: float = 0.0
    rotation_output: float = 0.0
    pressure_output: float = 0.0

    # Flags
    feed_is_raw: bool = False
    rotation_is_raw: bool = False
    pressure_is_raw: bool = False

    # Timestamps
    last_setpoint_time: Optional[float] = None
    last_feedback_time: Optional[float] = None


class DrillRegulatorNode(BaseNode):
    """Low-level drill regulator: 3 PIDs, safety checks, normalized outputs."""

    def __init__(self):
        super().__init__('drill_regulator_node')

        self.state = RegulatorState()
        self.current_mode: Optional[str] = None
        self.robomode: bool = False
        self.move_permission: bool = False

        self.feed_pid: Optional[PID] = None
        self.press_pid: Optional[PID] = None
        self.rot_pid: Optional[PID] = None

        self.setpoint_sources: Dict[str, Optional[Dict[str, Any]]] = {
            'driller': None,
            'rod_changer': None,
            'main_sm': None,
            'remote': None,
        }

    # ------------------------------------------------------------------
    # Lifecycle
    # ------------------------------------------------------------------
    def initialize(self):
        self._create_pid_controllers()
        self._create_publishers()
        self._create_subscribers()

    # ------------------------------------------------------------------
    # Setup helpers
    # ------------------------------------------------------------------
    def _create_pid_controllers(self):
        feed = self.node_params['feed_reg']
        press = self.node_params['press_reg']
        rot = self.node_params['rot_reg']

        self.feed_pid = PID(
            self.get_time,
            self,
            p=feed['p'], i=feed['i'], d=feed['d'], ff=feed['ff'],
            i_saturation=feed['i_saturation'], out_min=feed['out_min'], out_max=feed['out_max'],
            d_term_tc=feed['d_term_tc'], out_tc=feed['out_tc'],
            force_zero=True, name='FEED', enable_logging=True,
        )

        self.press_pid = PID(
            self.get_time,
            self,
            p=press['p'], i=press['i'], d=press['d'], ff=press['ff'],
            i_saturation=press['i_saturation'], out_min=press['out_min'], out_max=press['out_max'],
            d_term_tc=press['d_term_tc'], out_tc=press['out_tc'],
            force_zero=True, name='PRESS', enable_logging=True,
        )

        self.rot_pid = PID(
            self.get_time,
            self,
            p=rot['p'], i=rot['i'], d=rot['d'], ff=rot['ff'],
            i_saturation=rot['i_saturation'], out_min=rot['out_min'], out_max=rot['out_max'],
            d_term_tc=rot['d_term_tc'], out_tc=rot['out_tc'],
            force_zero=True, name='ROT', enable_logging=True,
        )

    def _create_publishers(self):
        # Output always normalized [-1..1]
        self.drill_actuator_pub = self.create_publisher(DrillActuatorCtrl, 'drill_ctrl', 10)

    def _create_subscribers(self):
        # Setpoints from sources (ROS2 topics per new architecture)
        self.create_subscription(DrillCtrl, '/driller_setpoints', lambda m: self._setpoint_callback(m, 'driller'), 10)
        self.create_subscription(DrillCtrl, '/rod_changer_setpoints', lambda m: self._setpoint_callback(m, 'rod_changer'), 10)
        self.create_subscription(DrillCtrl, '/main_sm_drill_out', lambda m: self._setpoint_callback(m, 'main_sm'), 10)
        self.create_subscription(DrillCtrl, '/remote_drilling', lambda m: self._setpoint_callback(m, 'remote'), 10)

        # Feedback and status
        self.create_subscription(DrillState, '/drill_state', self._drill_state_callback, 10)

        # Use TRANSIENT_LOCAL for latched status topics to get last known values on startup
        qos_transient = QoSProfile(depth=10, durability=DurabilityPolicy.TRANSIENT_LOCAL, reliability=ReliabilityPolicy.RELIABLE)
        self.create_subscription(StateMachineStatus, '/main_state_machine_status', self._main_sm_status_callback, qos_profile=qos_transient)
        self.create_subscription(BoolStamped, '/robomode', lambda m: setattr(self, 'robomode', bool(m.value)), qos_profile=qos_transient)
        self.create_subscription(Permission, '/permission', lambda m: setattr(self, 'move_permission', bool(m.permission)), qos_profile=qos_transient)

    # ------------------------------------------------------------------
    # Main loop
    # ------------------------------------------------------------------
    def do_work(self):
        if not self._safety_check():
            self._stop_control()
            self._publish_control()
            return

        active = self._select_active_setpoints()
        if not active:
            self._stop_control()
            self._publish_control()
            return

        self._update_setpoints(active)
        self._update_pid_controllers()
        self._publish_control()

    # ------------------------------------------------------------------
    # Logic helpers
    # ------------------------------------------------------------------
    @staticmethod
    def _clamp(value: float, vmin: float = -1.0, vmax: float = 1.0) -> float:
        return max(vmin, min(vmax, float(value)))
    def _safety_check(self) -> bool:
        now = self.get_time()
        timeout = self.node_params['msg_timeout']

        # Feedback freshness
        if self.state.last_feedback_time is None or now - self.state.last_feedback_time > timeout:
            self.log('Устаревшие данные состояния бурения', level=self.ERROR, event_code=self.events.SENSOR_FAILURE, period=1.0)
            return False
        # Setpoint freshness
        if self.state.last_setpoint_time is None or now - self.state.last_setpoint_time > timeout:
            self.log('Устаревшие уставки управления', level=self.ERROR, event_code=self.events.SW_ERROR, period=1.0)
            return False
        # Permissions
        if not self.robomode or not self.move_permission:
            return False
        # Mode allowlist
        allowed = ('drilling', 'calib', 'post_calib', 'restore_string', 'shaft_buildup', 'shaft_stow', 'remote')
        if self.current_mode not in allowed:
            return False
        return True

    def _select_active_setpoints(self) -> Optional[Dict[str, Any]]:
        mode = self.current_mode
        if mode == 'drilling':
            return self.setpoint_sources.get('driller')
        if mode in ('calib', 'post_calib', 'restore_string'):
            return self.setpoint_sources.get('main_sm')
        if mode in ('shaft_buildup', 'shaft_stow'):
            return self.setpoint_sources.get('rod_changer')
        if mode == 'remote':
            return self.setpoint_sources.get('remote')
        return None

    def _update_setpoints(self, sp: Dict[str, Any]):
        self.state.feed_setpoint = float(sp['feed_speed'])
        self.state.rotation_setpoint = float(sp['rotation_speed'])
        self.state.pressure_setpoint = float(sp['feed_pressure'])
        self.state.feed_is_raw = bool(sp['feed_speed_is_raw'])
        self.state.rotation_is_raw = bool(sp['rotation_speed_is_raw'])
        self.state.pressure_is_raw = bool(sp['feed_pressure_is_raw'])

    def _update_pid_controllers(self):
        # Feed: raw=True => absolute units -> PID; raw=False => normalized pass-through
        if self.state.feed_is_raw:
            self.state.feed_output = self.feed_pid.update(self.state.feed_setpoint, self.state.feed_feedback)
        else:
            self.feed_pid.reset()
            self.state.feed_output = self._clamp(self.state.feed_setpoint)

        # Pressure: raw=True => absolute units -> PID; raw=False => normalized with free-downwards logic
        if self.state.pressure_is_raw:
            self.state.pressure_output = self.press_pid.update(self.state.pressure_setpoint, self.state.pressure_feedback)
        else:
            if self.state.pressure_setpoint < 0.001:  # free moving downwards
                self.press_pid.reset()
                self.state.pressure_output = float(self.node_params['pdf_free_moving'])
            else:
                self.press_pid.reset()
                self.state.pressure_output = self._clamp(self.state.pressure_setpoint)

        # Rotation: raw=True => absolute rpm -> PID with sign-corrected feedback; else pass-through
        if self.state.rotation_is_raw:
            fb = self.state.rotation_feedback if self.state.rotation_setpoint >= 0 else -self.state.rotation_feedback
            self.state.rotation_output = self.rot_pid.update(self.state.rotation_setpoint, fb)
        else:
            self.rot_pid.reset()
            self.state.rotation_output = self._clamp(self.state.rotation_setpoint)

    def _stop_control(self):
        self.state.feed_output = 0.0
        self.state.rotation_output = 0.0
        self.state.pressure_output = 0.0
        if self.feed_pid: self.feed_pid.reset()
        if self.press_pid: self.press_pid.reset()
        if self.rot_pid: self.rot_pid.reset()

    def _publish_control(self):
        msg = DrillActuatorCtrl()
        msg.header.stamp = self.get_rostime()
        msg.feed_speed = float(self.state.feed_output)
        msg.rotation_speed = float(self.state.rotation_output)
        msg.feed_pressure = float(self.state.pressure_output)
        base_torque = self.state.torque_limit if self.state.torque_limit > 0.001 else float(self.node_params['default_rot_torque'])
        msg.torque_limit = self._clamp(base_torque)
        self.drill_actuator_pub.publish(msg)

    # ------------------------------------------------------------------
    # Callbacks
    # ------------------------------------------------------------------
    def _setpoint_callback(self, msg: DrillCtrl, source: str):
        self.setpoint_sources[source] = {
            'feed_speed': msg.feed_speed,
            'rotation_speed': msg.rotation_speed,
            'feed_pressure': msg.feed_pressure,
            'feed_speed_is_raw': msg.feed_speed_is_raw,
            'rotation_speed_is_raw': msg.rotation_speed_is_raw,
            'feed_pressure_is_raw': msg.feed_pressure_is_raw,
        }
        if self._is_active_source(source):
            self.state.last_setpoint_time = self.get_time()

    def _drill_state_callback(self, msg: DrillState):
        self.state.feed_feedback = float(msg.head_speed)
        self.state.rotation_feedback = float(msg.drill_rpm)
        self.state.pressure_feedback = float(msg.feed_pressure)
        self.state.last_feedback_time = self.get_time()

    def _main_sm_status_callback(self, msg: StateMachineStatus):
        self.current_mode = (msg.current_state or '').lower()

    def _is_active_source(self, source: str) -> bool:
        mode = self.current_mode
        return (
            (mode == 'drilling' and source == 'driller') or
            (mode in ('calib', 'post_calib', 'restore_string') and source == 'main_sm') or
            (mode in ('shaft_buildup', 'shaft_stow') and source == 'rod_changer') or
            (mode == 'remote' and source == 'remote')
        )

    # ------------------------------------------------------------------
    # Params update
    # ------------------------------------------------------------------
    def on_params_update(self, _keys):
        for (pid, key) in (
            (self.feed_pid, 'feed_reg'),
            (self.press_pid, 'press_reg'),
            (self.rot_pid, 'rot_reg'),
        ):
            if pid is None:
                continue
            prm = self.node_params[key]
            pid.p = prm['p']
            pid.i = prm['i']
            pid.d = prm['d']
            pid.ff = prm['ff']
            pid.i_saturation = prm['i_saturation']
            pid.out_min = prm['out_min']
            pid.out_max = prm['out_max']
            pid.d_term_tc = prm['d_term_tc']
            pid.out_tc = prm['out_tc']
        self.log('PID параметры обновлены', level=self.INFO)


def main(args=None):
    rclpy.init(args=args)
    node = DrillRegulatorNode()
    try:
        node.run()
    except KeyboardInterrupt:
        pass
    finally:
        rclpy.shutdown()