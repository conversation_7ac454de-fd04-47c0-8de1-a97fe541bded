[0.301s] DEBUG:colcon:Command line arguments: ['/Users/<USER>/.ros2_venv/bin/colcon', 'build', '--symlink-install', '--cmake-args', '-DCMAKE_CXX_FLAGS=-I/opt/homebrew/include', '--packages-select', 'drill_regulator']
[0.301s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['drill_regulator'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, cmake_args=['-DCMAKE_CXX_FLAGS=-I/opt/homebrew/include'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x103f67050>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x103f66790>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x103f66790>>)
[0.500s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.500s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.500s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.500s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.500s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.500s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.500s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/Users/<USER>/Work/drill2/onboard'
[0.500s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['ignore', 'ignore_ament_install']
[0.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'ignore'
[0.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'ignore_ament_install'
[0.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['colcon_pkg']
[0.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'colcon_pkg'
[0.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['colcon_meta']
[0.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'colcon_meta'
[0.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['ros']
[0.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'ros'
[0.517s] DEBUG:colcon.colcon_core.package_identification:Package 'src/arm_controller' with type 'ros.ament_python' and name 'arm_controller'
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ignore', 'ignore_ament_install']
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore'
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore_ament_install'
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_pkg']
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_pkg'
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_meta']
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_meta'
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ros']
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ros'
[0.518s] DEBUG:colcon.colcon_core.package_identification:Package 'src/base_node' with type 'ros.ament_python' and name 'base_node'
[0.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/build) by extensions ['ignore', 'ignore_ament_install']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/build) by extension 'ignore'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/build) ignored
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ignore', 'ignore_ament_install']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore_ament_install'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_pkg']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_pkg'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_meta']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_meta'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ros']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ros'
[0.519s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_decoder' with type 'ros.ament_python' and name 'can_decoder'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ignore', 'ignore_ament_install']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore_ament_install'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_pkg']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_pkg'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_meta']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_meta'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ros']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ros'
[0.520s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_encoder' with type 'ros.ament_python' and name 'can_encoder'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore_ament_install'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_pkg']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_pkg'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_meta']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_meta'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ros']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ros'
[0.521s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_msgs' with type 'ros.ament_cmake' and name 'can_msgs'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ignore', 'ignore_ament_install']
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore_ament_install'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_pkg']
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_pkg'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_meta']
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_meta'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ros']
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ros'
[0.522s] DEBUG:colcon.colcon_core.package_identification:Package 'src/depth_tracker' with type 'ros.ament_python' and name 'depth_tracker'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ignore', 'ignore_ament_install']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore_ament_install'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_pkg']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_pkg'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_meta']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_meta'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ros']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ros'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['cmake', 'python']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'cmake'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['python_setup_py']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python_setup_py'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore_ament_install'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_pkg']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_pkg'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_meta']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_meta'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ros']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ros'
[0.523s] DEBUG:colcon.colcon_core.package_identification:Package 'src/drill_msgs' with type 'ros.ament_cmake' and name 'drill_msgs'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ignore'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ignore_ament_install'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['colcon_pkg']
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'colcon_pkg'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['colcon_meta']
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'colcon_meta'
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['ros']
[0.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ros'
[0.524s] DEBUG:colcon.colcon_core.package_identification:Package 'src/drill_regulator' with type 'ros.ament_python' and name 'drill_regulator'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['ignore', 'ignore_ament_install']
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'ignore'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'ignore_ament_install'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['colcon_pkg']
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'colcon_pkg'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['colcon_meta']
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'colcon_meta'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['ros']
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'ros'
[0.525s] DEBUG:colcon.colcon_core.package_identification:Package 'src/driller' with type 'ros.ament_python' and name 'driller'
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['ignore', 'ignore_ament_install']
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'ignore'
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) ignored
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['ignore', 'ignore_ament_install']
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ignore'
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ignore_ament_install'
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['colcon_pkg']
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'colcon_pkg'
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['colcon_meta']
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'colcon_meta'
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['ros']
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ros'
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['cmake', 'python']
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'cmake'
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'python'
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['python_setup_py']
[0.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'python_setup_py'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['ignore', 'ignore_ament_install']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ignore'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ignore_ament_install'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['colcon_pkg']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'colcon_pkg'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['colcon_meta']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'colcon_meta'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['ros']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ros'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['cmake', 'python']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'cmake'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'python'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['python_setup_py']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'python_setup_py'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) by extensions ['ignore', 'ignore_ament_install']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) by extension 'ignore'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) ignored
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['ignore', 'ignore_ament_install']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ignore'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ignore_ament_install'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['colcon_pkg']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'colcon_pkg'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['colcon_meta']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'colcon_meta'
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['ros']
[0.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ros'
[0.527s] DEBUG:colcon.colcon_core.package_identification:Package 'src/external/maneuver-builder-cpp' with type 'ros.ament_cmake' and name 'maneuver_builder_cpp'
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['ignore', 'ignore_ament_install']
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ignore'
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ignore_ament_install'
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['colcon_pkg']
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'colcon_pkg'
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['colcon_meta']
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'colcon_meta'
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['ros']
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ros'
[0.527s] DEBUG:colcon.colcon_core.package_identification:Package 'src/external/pydubins' with type 'ros.ament_python' and name 'pydubins'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/fork_controller) by extensions ['ignore', 'ignore_ament_install']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/fork_controller) by extension 'ignore'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/fork_controller) by extension 'ignore_ament_install'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/fork_controller) by extensions ['colcon_pkg']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/fork_controller) by extension 'colcon_pkg'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/fork_controller) by extensions ['colcon_meta']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/fork_controller) by extension 'colcon_meta'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/fork_controller) by extensions ['ros']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/fork_controller) by extension 'ros'
[0.528s] DEBUG:colcon.colcon_core.package_identification:Package 'src/fork_controller' with type 'ros.ament_python' and name 'fork_controller'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['ignore', 'ignore_ament_install']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ignore'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ignore_ament_install'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['colcon_pkg']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'colcon_pkg'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['colcon_meta']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'colcon_meta'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['ros']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ros'
[0.529s] DEBUG:colcon.colcon_core.package_identification:Package 'src/hal_connector' with type 'ros.ament_python' and name 'hal_connector'
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/install) by extensions ['ignore', 'ignore_ament_install']
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/install) by extension 'ignore'
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/install) ignored
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['ignore', 'ignore_ament_install']
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ignore'
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ignore_ament_install'
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['colcon_pkg']
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'colcon_pkg'
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['colcon_meta']
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'colcon_meta'
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['ros']
[0.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ros'
[0.530s] DEBUG:colcon.colcon_core.package_identification:Package 'src/launchpack' with type 'ros.ament_python' and name 'launchpack'
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['ignore', 'ignore_ament_install']
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ignore'
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ignore_ament_install'
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['colcon_pkg']
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'colcon_pkg'
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['colcon_meta']
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'colcon_meta'
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['ros']
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ros'
[0.532s] DEBUG:colcon.colcon_core.package_identification:Package 'src/leveler' with type 'ros.ament_python' and name 'leveler'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) by extensions ['ignore', 'ignore_ament_install']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) by extension 'ignore'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) ignored
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['ignore', 'ignore_ament_install']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ignore'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ignore_ament_install'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['colcon_pkg']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'colcon_pkg'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['colcon_meta']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'colcon_meta'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['ros']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ros'
[0.533s] DEBUG:colcon.colcon_core.package_identification:Package 'src/main_state_machine' with type 'ros.ament_python' and name 'main_state_machine'
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['ignore', 'ignore_ament_install']
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ignore'
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ignore_ament_install'
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['colcon_pkg']
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'colcon_pkg'
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['colcon_meta']
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'colcon_meta'
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['ros']
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ros'
[0.533s] DEBUG:colcon.colcon_core.package_identification:Package 'src/modbus_node' with type 'ros.ament_python' and name 'modbus_node'
[0.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/old-controllers-ros1) by extensions ['ignore', 'ignore_ament_install']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/old-controllers-ros1) by extension 'ignore'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/old-controllers-ros1) ignored
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['ignore', 'ignore_ament_install']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ignore'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ignore_ament_install'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['colcon_pkg']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'colcon_pkg'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['colcon_meta']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'colcon_meta'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['ros']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ros'
[0.534s] DEBUG:colcon.colcon_core.package_identification:Package 'src/params_server' with type 'ros.ament_python' and name 'params_server'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['ignore', 'ignore_ament_install']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ignore'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ignore_ament_install'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['colcon_pkg']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'colcon_pkg'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['colcon_meta']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'colcon_meta'
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['ros']
[0.534s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ros'
[0.535s] DEBUG:colcon.colcon_core.package_identification:Package 'src/path_follower' with type 'ros.ament_python' and name 'path_follower'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['ignore', 'ignore_ament_install']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ignore'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ignore_ament_install'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['colcon_pkg']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'colcon_pkg'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['colcon_meta']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'colcon_meta'
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['ros']
[0.535s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ros'
[0.536s] DEBUG:colcon.colcon_core.package_identification:Package 'src/remote_connector' with type 'ros.ament_python' and name 'remote_connector'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['ignore', 'ignore_ament_install']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ignore'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ignore_ament_install'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['colcon_pkg']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'colcon_pkg'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['colcon_meta']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'colcon_meta'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['ros']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ros'
[0.541s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros-foxglove-bridge' with type 'ros.ament_cmake' and name 'foxglove_bridge'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['ignore', 'ignore_ament_install']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ignore'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ignore_ament_install'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['colcon_pkg']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'colcon_pkg'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['colcon_meta']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'colcon_meta'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['ros']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ros'
[0.555s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rosx_introspection' with type 'ros.ament_cmake' and name 'rosx_introspection'
[0.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['ignore', 'ignore_ament_install']
[0.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ignore'
[0.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ignore_ament_install'
[0.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['colcon_pkg']
[0.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'colcon_pkg'
[0.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['colcon_meta']
[0.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'colcon_meta'
[0.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['ros']
[0.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ros'
[0.556s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rtk_connector' with type 'ros.ament_python' and name 'rtk_connector'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ignore'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ignore_ament_install'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['colcon_pkg']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'colcon_pkg'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['colcon_meta']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'colcon_meta'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['ros']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ros'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['cmake', 'python']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'cmake'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'python'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['python_setup_py']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'python_setup_py'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['ignore', 'ignore_ament_install']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ignore'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ignore_ament_install'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['colcon_pkg']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'colcon_pkg'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['colcon_meta']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'colcon_meta'
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['ros']
[0.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ros'
[0.557s] DEBUG:colcon.colcon_core.package_identification:Package 'src/state_tracker' with type 'ros.ament_python' and name 'state_tracker'
[0.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ignore'
[0.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ignore_ament_install'
[0.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['colcon_pkg']
[0.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'colcon_pkg'
[0.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['colcon_meta']
[0.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'colcon_meta'
[0.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['ros']
[0.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ros'
[0.558s] DEBUG:colcon.colcon_core.package_identification:Package 'src/tracks_regulator' with type 'ros.ament_python' and name 'tracks_regulator'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['ignore', 'ignore_ament_install']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ignore'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ignore_ament_install'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['colcon_pkg']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'colcon_pkg'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['colcon_meta']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'colcon_meta'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['ros']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ros'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['cmake', 'python']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'cmake'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'python'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['python_setup_py']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'python_setup_py'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['ignore', 'ignore_ament_install']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ignore'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ignore_ament_install'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['colcon_pkg']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'colcon_pkg'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['colcon_meta']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'colcon_meta'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['ros']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ros'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['cmake', 'python']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'cmake'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'python'
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['python_setup_py']
[0.558s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'python_setup_py'
[0.559s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) by extensions ['ignore', 'ignore_ament_install']
[0.559s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) by extension 'ignore'
[0.559s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) ignored
[0.559s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) by extensions ['ignore', 'ignore_ament_install']
[0.559s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) by extension 'ignore'
[0.559s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) ignored
[0.559s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) by extensions ['ignore', 'ignore_ament_install']
[0.559s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) by extension 'ignore'
[0.559s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) ignored
[0.559s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.559s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.559s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.559s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.559s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_msgs' in 'src/can_msgs'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'drill_msgs' in 'src/drill_msgs'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'launchpack' in 'src/launchpack'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'maneuver_builder_cpp' in 'src/external/maneuver-builder-cpp'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pydubins' in 'src/external/pydubins'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rosx_introspection' in 'src/rosx_introspection'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'base_node' in 'src/base_node'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_decoder' in 'src/can_decoder'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_encoder' in 'src/can_encoder'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'foxglove_bridge' in 'src/ros-foxglove-bridge'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'main_state_machine' in 'src/main_state_machine'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'params_server' in 'src/params_server'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'remote_connector' in 'src/remote_connector'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rtk_connector' in 'src/rtk_connector'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'state_tracker' in 'src/state_tracker'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'arm_controller' in 'src/arm_controller'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'depth_tracker' in 'src/depth_tracker'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'driller' in 'src/driller'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'fork_controller' in 'src/fork_controller'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'hal_connector' in 'src/hal_connector'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'leveler' in 'src/leveler'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'modbus_node' in 'src/modbus_node'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'path_follower' in 'src/path_follower'
[0.607s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'tracks_regulator' in 'src/tracks_regulator'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_args' from command line to '['-DCMAKE_CXX_FLAGS=-I/opt/homebrew/include']'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_target' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_clean_cache' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_clean_first' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_force_configure' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'ament_cmake_args' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'catkin_cmake_args' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.607s] DEBUG:colcon.colcon_core.verb:Building package 'drill_regulator' with the following arguments: {'ament_cmake_args': None, 'build_base': '/Users/<USER>/Work/drill2/onboard/build/drill_regulator', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CXX_FLAGS=-I/opt/homebrew/include'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/Users/<USER>/Work/drill2/onboard/install/drill_regulator', 'merge_install': False, 'path': '/Users/<USER>/Work/drill2/onboard/src/drill_regulator', 'symlink_install': True, 'test_result_base': None}
[0.607s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.608s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.609s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/Users/<USER>/Work/drill2/onboard/src/drill_regulator' with build type 'ament_python'
[0.609s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_regulator', 'ament_prefix_path')
[0.614s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.614s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/hook/ament_prefix_path.ps1'
[0.615s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/hook/ament_prefix_path.dsv'
[0.616s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/hook/ament_prefix_path.sh'
[0.616s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.616s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.797s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/Users/<USER>/Work/drill2/onboard/src/drill_regulator'
[1.797s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.797s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.038s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator': AMENT_PREFIX_PATH=/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge:/Users/<USER>/Work/drill2/onboard/install/rosx_introspection:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/arm_controller:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format CMAKE_PREFIX_PATH=/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdfdom:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/urdfdom_headers:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection:/Users/<USER>/ros2_jazzy/install/cyclonedds:/Users/<USER>/ros2_jazzy/install/iceoryx_posh:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/gmock_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps:/Users/<USER>/ros2_jazzy/install/fastcdr:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/gtest_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_index_cpp DYLD_LIBRARY_PATH=/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge/lib:/Users/<USER>/Work/drill2/onboard/install/rosx_introspection/lib:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd/lib:/Users/<USER>/ros2_jazzy/install/mcap_vendor/lib:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins/lib:/Users/<USER>/ros2_jazzy/install/rviz_common/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_transport/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage/lib:/Users/<USER>/ros2_jazzy/install/camera_info_manager/lib:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers/lib:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib:/Users/<USER>/ros2_jazzy/install/robot_state_publisher/lib:/Users/<USER>/ros2_jazzy/install/kdl_parser/lib:/Users/<USER>/ros2_jazzy/install/urdf/lib:/Users/<USER>/ros2_jazzy/install/urdfdom/lib:/Users/<USER>/ros2_jazzy/install/turtlesim/lib:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_ros/lib:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl/lib:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib:/Users/<USER>/ros2_jazzy/install/tf2/lib:/Users/<USER>/ros2_jazzy/install/test_msgs/lib:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_srvs/lib:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp/lib:/Users/<USER>/ros2_jazzy/install/point_cloud_transport/lib:/Users/<USER>/ros2_jazzy/install/map_msgs/lib:/Users/<USER>/ros2_jazzy/install/image_transport/lib:/Users/<USER>/ros2_jazzy/install/image_tools/lib:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib:/Users/<USER>/ros2_jazzy/install/message_filters/lib:/Users/<USER>/ros2_jazzy/install/logging_demo/lib:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp/lib:/Users/<USER>/ros2_jazzy/install/composition/lib:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_components/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_action/lib:/Users/<USER>/ros2_jazzy/install/rclcpp/lib:/Users/<USER>/ros2_jazzy/install/libstatistics_collector/lib:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclpy/lib:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/rcl_action/lib:/Users/<USER>/ros2_jazzy/install/rcl/lib:/Users/<USER>/ros2_jazzy/install/rmw_implementation/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_msgs/lib:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib:/Users/<USER>/ros2_jazzy/install/service_msgs/lib:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog/lib:/Users/<USER>/ros2_jazzy/install/rviz_rendering/lib:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps/lib:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser/lib:/Users/<USER>/ros2_jazzy/install/rmw/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/lib:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib:/Users/<USER>/ros2_jazzy/install/class_loader/lib:/Users/<USER>/ros2_jazzy/install/rcpputils/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface/lib:/Users/<USER>/ros2_jazzy/install/rcutils/lib:/Users/<USER>/ros2_jazzy/install/performance_test_fixture/lib:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib:/Users/<USER>/ros2_jazzy/install/mimick_vendor/opt/mimick_vendor/lib:/Users/<USER>/ros2_jazzy/install/keyboard_handler/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor/lib:/Users/<USER>/ros2_jazzy/install/fastrtps/lib:/Users/<USER>/ros2_jazzy/install/fastcdr/lib:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib:/Users/<USER>/ros2_jazzy/install/ament_index_cpp/lib PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/base_node:/Users/<USER>/Work/drill2/onboard/install/base_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/state_tracker:/Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/remote_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/path_follower:/Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/params_server:/Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/leveler:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/launchpack:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/driller:/Users/<USER>/Work/drill2/onboard/install/driller/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/drill_regulator:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/can_decoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/arm_controller:/Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/launch_testing_examples/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2bag:/Users/<USER>/ros2_jazzy/install/ros2bag/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/turtlesim/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_launch:/Users/<USER>/ros2_jazzy/install/tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/topic_monitor:/Users/<USER>/ros2_jazzy/install/topic_monitor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_tools/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_kdl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/examples_tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/test_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_srvs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/map_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2doctor:/Users/<USER>/ros2_jazzy/install/ros2doctor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/message_filters/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/logging_demo/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2component:/Users/<USER>/ros2_jazzy/install/ros2component/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sros2:/Users/<USER>/ros2_jazzy/install/sros2/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2topic/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2test:/Users/<USER>/ros2_jazzy/install/ros2test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2param:/Users/<USER>/ros2_jazzy/install/ros2param/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2lifecycle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2service:/Users/<USER>/ros2_jazzy/install/ros2service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2run:/Users/<USER>/ros2_jazzy/install/ros2run/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2pkg/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2node:/Users/<USER>/ros2_jazzy/install/ros2node/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2multicast/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2interface/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2action:/Users/<USER>/ros2_jazzy/install/ros2action/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2cli:/Users/<USER>/ros2_jazzy/install/ros2cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_testing_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_ros:/Users/<USER>/ros2_jazzy/install/launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/action_tutorials_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rclpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/service_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools_read/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lttngpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rpyutils:/Users/<USER>/ros2_jazzy/install/rpyutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_parser/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_adapter/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosidl_cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_qt_binding/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor/lib/python3.11/dist-packages:/Users/<USER>/ros2_jazzy/build/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_pytest/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_testing/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_yaml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_xml:/Users/<USER>/ros2_jazzy/install/launch_xml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch:/Users/<USER>/ros2_jazzy/install/launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/osrf_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/domain_coordinator:/Users/<USER>/ros2_jazzy/install/domain_coordinator/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_package:/Users/<USER>/ros2_jazzy/install/ament_package/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_mypy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_flake8/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_copyright/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_lint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_python/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cpplint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_format:/Users/<USER>/ros2_jazzy/install/ament_clang_format/lib/python3.11/site-packages SHLVL=3 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
[3.880s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_regulator', 'pythonpath_develop')
[3.881s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/build/drill_regulator/share/drill_regulator/hook/pythonpath_develop.ps1'
[3.881s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator' returned '0': AMENT_PREFIX_PATH=/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge:/Users/<USER>/Work/drill2/onboard/install/rosx_introspection:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/arm_controller:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format CMAKE_PREFIX_PATH=/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdfdom:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/urdfdom_headers:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection:/Users/<USER>/ros2_jazzy/install/cyclonedds:/Users/<USER>/ros2_jazzy/install/iceoryx_posh:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/gmock_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps:/Users/<USER>/ros2_jazzy/install/fastcdr:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/gtest_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_index_cpp DYLD_LIBRARY_PATH=/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib:/Users/<USER>/Work/drill2/onboard/install/foxglove_bridge/lib:/Users/<USER>/Work/drill2/onboard/install/rosx_introspection/lib:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd/lib:/Users/<USER>/ros2_jazzy/install/mcap_vendor/lib:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins/lib:/Users/<USER>/ros2_jazzy/install/rviz_common/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_transport/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage/lib:/Users/<USER>/ros2_jazzy/install/camera_info_manager/lib:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers/lib:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib:/Users/<USER>/ros2_jazzy/install/robot_state_publisher/lib:/Users/<USER>/ros2_jazzy/install/kdl_parser/lib:/Users/<USER>/ros2_jazzy/install/urdf/lib:/Users/<USER>/ros2_jazzy/install/urdfdom/lib:/Users/<USER>/ros2_jazzy/install/turtlesim/lib:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_ros/lib:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl/lib:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib:/Users/<USER>/ros2_jazzy/install/tf2/lib:/Users/<USER>/ros2_jazzy/install/test_msgs/lib:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_srvs/lib:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp/lib:/Users/<USER>/ros2_jazzy/install/point_cloud_transport/lib:/Users/<USER>/ros2_jazzy/install/map_msgs/lib:/Users/<USER>/ros2_jazzy/install/image_transport/lib:/Users/<USER>/ros2_jazzy/install/image_tools/lib:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib:/Users/<USER>/ros2_jazzy/install/message_filters/lib:/Users/<USER>/ros2_jazzy/install/logging_demo/lib:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp/lib:/Users/<USER>/ros2_jazzy/install/composition/lib:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_components/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_action/lib:/Users/<USER>/ros2_jazzy/install/rclcpp/lib:/Users/<USER>/ros2_jazzy/install/libstatistics_collector/lib:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclpy/lib:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/rcl_action/lib:/Users/<USER>/ros2_jazzy/install/rcl/lib:/Users/<USER>/ros2_jazzy/install/rmw_implementation/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_msgs/lib:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib:/Users/<USER>/ros2_jazzy/install/service_msgs/lib:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog/lib:/Users/<USER>/ros2_jazzy/install/rviz_rendering/lib:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps/lib:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser/lib:/Users/<USER>/ros2_jazzy/install/rmw/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/lib:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib:/Users/<USER>/ros2_jazzy/install/class_loader/lib:/Users/<USER>/ros2_jazzy/install/rcpputils/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface/lib:/Users/<USER>/ros2_jazzy/install/rcutils/lib:/Users/<USER>/ros2_jazzy/install/performance_test_fixture/lib:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib:/Users/<USER>/ros2_jazzy/install/mimick_vendor/opt/mimick_vendor/lib:/Users/<USER>/ros2_jazzy/install/keyboard_handler/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor/lib:/Users/<USER>/ros2_jazzy/install/fastrtps/lib:/Users/<USER>/ros2_jazzy/install/fastcdr/lib:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib:/Users/<USER>/ros2_jazzy/install/ament_index_cpp/lib PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/base_node:/Users/<USER>/Work/drill2/onboard/install/base_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/state_tracker:/Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/remote_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/path_follower:/Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/params_server:/Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/leveler:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/launchpack:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/driller:/Users/<USER>/Work/drill2/onboard/install/driller/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/drill_regulator:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/can_decoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/build/arm_controller:/Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/launch_testing_examples/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2bag:/Users/<USER>/ros2_jazzy/install/ros2bag/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/turtlesim/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_launch:/Users/<USER>/ros2_jazzy/install/tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/topic_monitor:/Users/<USER>/ros2_jazzy/install/topic_monitor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_tools/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_kdl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/examples_tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/test_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_srvs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/map_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2doctor:/Users/<USER>/ros2_jazzy/install/ros2doctor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/message_filters/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/logging_demo/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2component:/Users/<USER>/ros2_jazzy/install/ros2component/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sros2:/Users/<USER>/ros2_jazzy/install/sros2/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2topic/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2test:/Users/<USER>/ros2_jazzy/install/ros2test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2param:/Users/<USER>/ros2_jazzy/install/ros2param/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2lifecycle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2service:/Users/<USER>/ros2_jazzy/install/ros2service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2run:/Users/<USER>/ros2_jazzy/install/ros2run/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2pkg/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2node:/Users/<USER>/ros2_jazzy/install/ros2node/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2multicast/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2interface/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2action:/Users/<USER>/ros2_jazzy/install/ros2action/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2cli:/Users/<USER>/ros2_jazzy/install/ros2cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_testing_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_ros:/Users/<USER>/ros2_jazzy/install/launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/action_tutorials_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rclpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/service_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools_read/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lttngpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rpyutils:/Users/<USER>/ros2_jazzy/install/rpyutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_parser/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_adapter/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosidl_cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_qt_binding/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor/lib/python3.11/dist-packages:/Users/<USER>/ros2_jazzy/build/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_pytest/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_testing/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_yaml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_xml:/Users/<USER>/ros2_jazzy/install/launch_xml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch:/Users/<USER>/ros2_jazzy/install/launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/osrf_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/domain_coordinator:/Users/<USER>/ros2_jazzy/install/domain_coordinator/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_package:/Users/<USER>/ros2_jazzy/install/ament_package/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_mypy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_flake8/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_copyright/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_lint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_python/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cpplint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_format:/Users/<USER>/ros2_jazzy/install/ament_clang_format/lib/python3.11/site-packages SHLVL=3 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
[3.881s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/build/drill_regulator/share/drill_regulator/hook/pythonpath_develop.dsv'
[3.882s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/build/drill_regulator/share/drill_regulator/hook/pythonpath_develop.sh'
[3.889s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_regulator' for CMake module files
[3.889s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_regulator' for CMake config files
[3.890s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib'
[3.890s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/bin'
[3.890s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/pkgconfig/drill_regulator.pc'
[3.890s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages'
[3.890s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_regulator', 'pythonpath')
[3.890s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/hook/pythonpath.ps1'
[3.891s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/hook/pythonpath.dsv'
[3.891s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/hook/pythonpath.sh'
[3.891s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/bin'
[3.891s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(drill_regulator)
[3.892s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/package.ps1'
[3.892s] INFO:colcon.colcon_core.shell:Creating package descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/package.dsv'
[3.892s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/package.sh'
[3.893s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/package.bash'
[3.893s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/drill_regulator/package.zsh'
[3.894s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/Users/<USER>/Work/drill2/onboard/install/drill_regulator/share/colcon-core/packages/drill_regulator)
[3.894s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[3.894s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[3.894s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[3.894s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[3.897s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify2': 'notify2' not found
[3.897s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify_send': Not used on non-Linux systems
[3.897s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[3.897s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'terminal_notifier'
[3.983s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[3.984s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.ps1'
[3.985s] INFO:colcon.colcon_core.shell:Creating prefix util module '/Users/<USER>/Work/drill2/onboard/install/_local_setup_util_ps1.py'
[3.986s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.ps1'
[4.000s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.sh'
[4.001s] INFO:colcon.colcon_core.shell:Creating prefix util module '/Users/<USER>/Work/drill2/onboard/install/_local_setup_util_sh.py'
[4.001s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.sh'
[4.015s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.bash'
[4.015s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.bash'
[4.034s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.zsh'
[4.035s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.zsh'
