from setuptools import setup, find_packages

package_name = 'drill_regulator'

setup(
    name=package_name,
    version='0.0.1',
    packages=find_packages(),
    data_files=[
        ('share/ament_index/resource_index/packages', ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/drill_regulator.launch.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Roman Fedorenko',
    maintainer_email='<EMAIL>',
    description='Drill low-level regulator with three PIDs',
    license='Proprietary',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'drill_regulator_node = drill_regulator.drill_regulator_node:main',
        ],
    },
)
